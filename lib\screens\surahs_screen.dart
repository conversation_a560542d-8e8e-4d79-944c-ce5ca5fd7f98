import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../models/surah.dart';
import '../widgets/surah_card.dart';

/// شاشة السور
class SurahsScreen extends StatefulWidget {
  const SurahsScreen({super.key});

  @override
  State<SurahsScreen> createState() => _SurahsScreenState();
}

class _SurahsScreenState extends State<SurahsScreen> {
  String _searchQuery = '';
  SurahFilter _currentFilter = SurahFilter.all;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('السور'),
        centerTitle: true,
        actions: [
          PopupMenuButton<SurahFilter>(
            icon: const Icon(Icons.filter_list),
            onSelected: (filter) {
              setState(() {
                _currentFilter = filter;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: SurahFilter.all,
                child: Text('جميع السور'),
              ),
              const PopupMenuItem(
                value: SurahFilter.makki,
                child: Text('السور المكية'),
              ),
              const PopupMenuItem(
                value: SurahFilter.madani,
                child: Text('السور المدنية'),
              ),
              const PopupMenuItem(
                value: SurahFilter.short,
                child: Text('السور القصيرة'),
              ),
              const PopupMenuItem(
                value: SurahFilter.long,
                child: Text('السور الطويلة'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'البحث في السور...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).cardColor,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // قائمة السور
          Expanded(
            child: Consumer<QuranProvider>(
              builder: (context, quranProvider, child) {
                if (quranProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                List<Surah> filteredSurahs = _getFilteredSurahs(quranProvider);

                if (filteredSurahs.isEmpty) {
                  return const Center(
                    child: Text('لا توجد سور تطابق البحث'),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredSurahs.length,
                  itemBuilder: (context, index) {
                    final surah = filteredSurahs[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: SurahCard(
                        surah: surah,
                        onTap: () => _navigateToSurah(surah),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Surah> _getFilteredSurahs(QuranProvider quranProvider) {
    List<Surah> surahs;

    // تطبيق الفلتر
    switch (_currentFilter) {
      case SurahFilter.makki:
        surahs = quranProvider.makkiSurahs;
        break;
      case SurahFilter.madani:
        surahs = quranProvider.madaniSurahs;
        break;
      case SurahFilter.short:
        surahs = quranProvider.shortSurahs;
        break;
      case SurahFilter.long:
        surahs = quranProvider.longSurahs;
        break;
      case SurahFilter.all:
        surahs = quranProvider.surahs;
        break;
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      surahs = surahs.where((surah) {
        return surah.nameArabic.contains(_searchQuery) ||
               surah.nameEnglish.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               surah.nameTransliteration.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    return surahs;
  }

  void _navigateToSurah(Surah surah) {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الانتقال إلى ${surah.nameArabic}')),
    );
  }
}

/// أنواع فلاتر السور
enum SurahFilter {
  all,
  makki,
  madani,
  short,
  long,
}
