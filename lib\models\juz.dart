/// نموذج الجزء
class Juz {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final List<JuzSurah> surahs;

  const <PERSON><PERSON>({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.surahs,
  });

  factory Juz.fromJson(Map<String, dynamic> json) {
    return Juz(
      number: json['number'] as int,
      nameArabic: json['name_arabic'] as String,
      nameEnglish: json['name_english'] as String,
      surahs: (json['surahs'] as List<dynamic>?)
              ?.map((surah) => JuzSurah.fromJson(surah as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'surahs': surahs.map((surah) => surah.toJson()).toList(),
    };
  }
}

/// نموذج السورة داخل الجزء
class JuzSurah {
  final int surahNumber;
  final String surahName;
  final int startAyah;
  final int endAyah;

  const <PERSON><PERSON><PERSON><PERSON><PERSON>({
    required this.surahNumber,
    required this.surahName,
    required this.startAyah,
    required this.endAyah,
  });

  factory JuzSurah.fromJson(Map<String, dynamic> json) {
    return JuzSurah(
      surahNumber: json['surah_number'] as int,
      surahName: json['surah_name'] as String,
      startAyah: json['start_ayah'] as int,
      endAyah: json['end_ayah'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surah_number': surahNumber,
      'surah_name': surahName,
      'start_ayah': startAyah,
      'end_ayah': endAyah,
    };
  }
}
