/// نموذج السورة
class Surah {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final int numberOfAyahs;
  final String revelationType; // مكية أو مدنية
  final List<Ayah> ayahs;

  const Sur<PERSON>({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.numberOfAyahs,
    required this.revelationType,
    required this.ayahs,
  });

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'] as int,
      nameArabic: json['name_arabic'] as String,
      nameEnglish: json['name_english'] as String,
      nameTransliteration: json['name_transliteration'] as String,
      numberOfAyahs: json['number_of_ayahs'] as int,
      revelationType: json['revelation_type'] as String,
      ayahs: (json['ayahs'] as List<dynamic>?)
              ?.map((ayah) => Ayah.fromJson(ayah as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'name_transliteration': nameTransliteration,
      'number_of_ayahs': numberOfAyahs,
      'revelation_type': revelationType,
      'ayahs': ayahs.map((ayah) => ayah.toJson()).toList(),
    };
  }
}

/// نموذج الآية
class Ayah {
  final int number;
  final int surahNumber;
  final String textArabic;
  final String textUthmani;
  final int juzNumber;
  final int hizbNumber;
  final int rubNumber;
  final int sajdah;

  const Ayah({
    required this.number,
    required this.surahNumber,
    required this.textArabic,
    required this.textUthmani,
    required this.juzNumber,
    required this.hizbNumber,
    required this.rubNumber,
    required this.sajdah,
  });

  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'] as int,
      surahNumber: json['surah_number'] as int,
      textArabic: json['text_arabic'] as String,
      textUthmani: json['text_uthmani'] as String,
      juzNumber: json['juz_number'] as int,
      hizbNumber: json['hizb_number'] as int,
      rubNumber: json['rub_number'] as int,
      sajdah: json['sajdah'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'surah_number': surahNumber,
      'text_arabic': textArabic,
      'text_uthmani': textUthmani,
      'juz_number': juzNumber,
      'hizb_number': hizbNumber,
      'rub_number': rubNumber,
      'sajdah': sajdah,
    };
  }

  /// معرف فريد للآية
  String get uniqueId => '${surahNumber}_$number';
}
