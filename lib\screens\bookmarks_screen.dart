import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bookmark_provider.dart';
import '../models/bookmark.dart';
import '../widgets/bookmark_card.dart';

/// شاشة الإشارات المرجعية
class BookmarksScreen extends StatefulWidget {
  const BookmarksScreen({super.key});

  @override
  State<BookmarksScreen> createState() => _BookmarksScreenState();
}

class _BookmarksScreenState extends State<BookmarksScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        centerTitle: true,
        actions: [
          PopupMenuButton<BookmarkSortType>(
            icon: const Icon(Icons.sort),
            onSelected: (sortType) {
              context.read<BookmarkProvider>().sortBookmarks(sortType);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: BookmarkSortType.dateNewest,
                child: Text('الأحدث أولاً'),
              ),
              const PopupMenuItem(
                value: BookmarkSortType.dateOldest,
                child: Text('الأقدم أولاً'),
              ),
              const PopupMenuItem(
                value: BookmarkSortType.surahNumber,
                child: Text('ترتيب السور'),
              ),
              const PopupMenuItem(
                value: BookmarkSortType.surahName,
                child: Text('اسم السورة'),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: BookmarkSearchDelegate(),
              );
            },
          ),
        ],
      ),
      body: Consumer<BookmarkProvider>(
        builder: (context, bookmarkProvider, child) {
          if (bookmarkProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final bookmarks = bookmarkProvider.bookmarks;

          if (bookmarks.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد إشارات مرجعية',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على أيقونة الإشارة المرجعية في أي آية لحفظها هنا',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: bookmarks.length,
            itemBuilder: (context, index) {
              final bookmark = bookmarks[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: BookmarkCard(
                  bookmark: bookmark,
                  onTap: () => _navigateToBookmark(bookmark),
                  onDelete: () => _deleteBookmark(bookmark),
                  onEdit: () => _editBookmark(bookmark),
                ),
              );
            },
          );
        },
      ),
    );
  }

  void _navigateToBookmark(Bookmark bookmark) {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الانتقال إلى ${bookmark.surahName} - الآية ${bookmark.ayahNumber}')),
    );
  }

  void _deleteBookmark(Bookmark bookmark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشارة المرجعية'),
        content: Text('هل تريد حذف الإشارة المرجعية من ${bookmark.surahName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<BookmarkProvider>().removeBookmark(bookmark.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الإشارة المرجعية')),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _editBookmark(Bookmark bookmark) {
    final noteController = TextEditingController(text: bookmark.note ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الملاحظة'),
        content: TextField(
          controller: noteController,
          decoration: const InputDecoration(
            hintText: 'أضف ملاحظة...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<BookmarkProvider>().updateBookmarkNote(
                bookmark.id,
                noteController.text.trim().isEmpty ? null : noteController.text.trim(),
              );
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تحديث الملاحظة')),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }
}

/// مفوض البحث في الإشارات المرجعية
class BookmarkSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'البحث في الإشارات المرجعية...';

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Consumer<BookmarkProvider>(
      builder: (context, bookmarkProvider, child) {
        bookmarkProvider.searchBookmarks(query);
        final results = bookmarkProvider.bookmarks;

        if (results.isEmpty) {
          return const Center(
            child: Text('لا توجد نتائج'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: results.length,
          itemBuilder: (context, index) {
            final bookmark = results[index];
            return BookmarkCard(
              bookmark: bookmark,
              onTap: () {
                close(context, bookmark.surahName);
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return buildResults(context);
  }
}
