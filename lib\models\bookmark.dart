/// نموذج الإشارة المرجعية
class Bookmark {
  final String id;
  final int surahNumber;
  final int ayahNumber;
  final String surahName;
  final String ayahText;
  final DateTime createdAt;
  final String? note;

  const Bookmark({
    required this.id,
    required this.surahNumber,
    required this.ayahNumber,
    required this.surahName,
    required this.ayahText,
    required this.createdAt,
    this.note,
  });

  factory Bookmark.fromJson(Map<String, dynamic> json) {
    return Bookmark(
      id: json['id'] as String,
      surahNumber: json['surah_number'] as int,
      ayahNumber: json['ayah_number'] as int,
      surahName: json['surah_name'] as String,
      ayahText: json['ayah_text'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      note: json['note'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'ayah_number': ayahNumber,
      'surah_name': surahName,
      'ayah_text': ayahText,
      'created_at': createdAt.toIso8601String(),
      'note': note,
    };
  }

  /// إنشاء إشارة مرجعية من آية
  factory Bookmark.fromAyah({
    required int surahNumber,
    required int ayahNumber,
    required String surahName,
    required String ayahText,
    String? note,
  }) {
    return Bookmark(
      id: '${surahNumber}_$ayahNumber',
      surahNumber: surahNumber,
      ayahNumber: ayahNumber,
      surahName: surahName,
      ayahText: ayahText,
      createdAt: DateTime.now(),
      note: note,
    );
  }
}
