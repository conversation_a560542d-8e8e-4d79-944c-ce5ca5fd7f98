import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../models/app_settings.dart';

/// شاشة الإعدادات
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        centerTitle: true,
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إعدادات المظهر
              _buildSectionHeader(context, 'المظهر'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('الوضع الليلي'),
                      subtitle: const Text('تفعيل المظهر الداكن'),
                      value: settingsProvider.isDarkMode,
                      onChanged: (_) => settingsProvider.toggleTheme(),
                      secondary: Icon(
                        settingsProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                      ),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('حجم الخط'),
                      subtitle: Text('${settingsProvider.fontSize.toInt()}'),
                      leading: const Icon(Icons.text_fields),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showFontSizeDialog(context, settingsProvider),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // إعدادات الصوت
              _buildSectionHeader(context, 'الصوت'),
              Card(
                child: Column(
                  children: [
                    ListTile(
                      title: const Text('القارئ'),
                      subtitle: Text(
                        AppSettings.availableReciters[settingsProvider.reciter] ?? 'غير محدد',
                      ),
                      leading: const Icon(Icons.person),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showReciterDialog(context, settingsProvider),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('سرعة التشغيل'),
                      subtitle: Text('${settingsProvider.settings.audioSpeed}x'),
                      leading: const Icon(Icons.speed),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showSpeedDialog(context, settingsProvider),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      title: const Text('التمرير التلقائي'),
                      subtitle: const Text('تمرير النص مع الصوت'),
                      value: settingsProvider.settings.autoScroll,
                      onChanged: (_) => settingsProvider.toggleAutoScroll(),
                      secondary: const Icon(Icons.autorenew),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // إعدادات أخرى
              _buildSectionHeader(context, 'أخرى'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('عرض الترجمة'),
                      subtitle: const Text('إظهار ترجمة الآيات'),
                      value: settingsProvider.settings.showTranslation,
                      onChanged: (_) => settingsProvider.toggleTranslation(),
                      secondary: const Icon(Icons.translate),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      title: const Text('الإشعارات'),
                      subtitle: const Text('تفعيل إشعارات التطبيق'),
                      value: settingsProvider.settings.enableNotifications,
                      onChanged: (_) => settingsProvider.toggleNotifications(),
                      secondary: const Icon(Icons.notifications),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('إعادة تعيين الإعدادات'),
                      subtitle: const Text('استعادة الإعدادات الافتراضية'),
                      leading: const Icon(Icons.restore),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showResetDialog(context, settingsProvider),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // معلومات التطبيق
              _buildSectionHeader(context, 'حول التطبيق'),
              Card(
                child: Column(
                  children: [
                    const ListTile(
                      title: Text('الإصدار'),
                      subtitle: Text('1.0.0'),
                      leading: Icon(Icons.info),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('المطور'),
                      subtitle: const Text('تطبيق القرآن الكريم'),
                      leading: const Icon(Icons.code),
                      onTap: () {
                        // معلومات المطور
                      },
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppSettings.availableFontSizes.map((size) {
            return RadioListTile<double>(
              title: Text('${size.toInt()}'),
              value: size,
              groupValue: settingsProvider.fontSize,
              onChanged: (value) {
                if (value != null) {
                  settingsProvider.setFontSize(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showReciterDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار القارئ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppSettings.availableReciters.entries.map((entry) {
            return RadioListTile<String>(
              title: Text(entry.value),
              value: entry.key,
              groupValue: settingsProvider.reciter,
              onChanged: (value) {
                if (value != null) {
                  settingsProvider.setReciter(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showSpeedDialog(BuildContext context, SettingsProvider settingsProvider) {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سرعة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: speeds.map((speed) {
            return RadioListTile<double>(
              title: Text('${speed}x'),
              value: speed,
              groupValue: settingsProvider.settings.audioSpeed,
              onChanged: (value) {
                if (value != null) {
                  settingsProvider.setAudioSpeed(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              settingsProvider.resetSettings();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إعادة تعيين الإعدادات')),
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
}
