import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../models/audio_state.dart';
import '../services/audio_service.dart';

/// مزود حالة الصوت
class AudioProvider with ChangeNotifier {
  final QuranAudioService _audioService = QuranAudioService();
  
  AudioState _audioState = const AudioState();
  AudioInfo? _currentAudioInfo;

  AudioState get audioState => _audioState;
  AudioInfo? get currentAudioInfo => _currentAudioInfo;
  AudioPlayer get audioPlayer => _audioService.audioPlayer;

  AudioProvider() {
    _initializeAudioPlayer();
  }

  /// تهيئة مشغل الصوت
  void _initializeAudioPlayer() {
    // الاستماع لحالة التشغيل
    _audioService.audioPlayer.playingStream.listen((isPlaying) {
      _audioState = _audioState.copyWith(isPlaying: isPlaying);
      notifyListeners();
    });

    // الاستماع لموضع التشغيل
    _audioService.audioPlayer.positionStream.listen((position) {
      _audioState = _audioState.copyWith(position: position);
      notifyListeners();
    });

    // الاستماع لمدة الملف
    _audioService.audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        _audioState = _audioState.copyWith(duration: duration);
        notifyListeners();
      }
    });

    // الاستماع لحالة المعالجة
    _audioService.audioPlayer.processingStateStream.listen((state) {
      _audioState = _audioState.copyWith(
        processingState: state,
        isLoading: state == ProcessingState.loading ||
                   state == ProcessingState.buffering,
      );
      notifyListeners();
    });
  }

  /// تشغيل سورة
  Future<void> playSurah(int surahNumber, String surahName, String reciter) async {
    try {
      _audioState = _audioState.copyWith(
        isLoading: true,
        currentSurah: surahNumber,
      );
      notifyListeners();

      _currentAudioInfo = AudioInfo(
        surahNumber: surahNumber,
        surahName: surahName,
        reciterName: reciter,
        audioUrl: '',
        isDownloaded: await _audioService.isAudioDownloaded(surahNumber, reciter),
      );

      await _audioService.playSurah(surahNumber, reciter);
    } catch (e) {
      _audioState = _audioState.copyWith(isLoading: false);
      notifyListeners();
      rethrow;
    }
  }

  /// إيقاف مؤقت
  Future<void> pause() async {
    await _audioService.pause();
  }

  /// استئناف التشغيل
  Future<void> resume() async {
    await _audioService.resume();
  }

  /// إيقاف التشغيل
  Future<void> stop() async {
    await _audioService.stop();
    _audioState = _audioState.copyWith(
      currentSurah: null,
      currentAyah: null,
    );
    _currentAudioInfo = null;
    notifyListeners();
  }

  /// الانتقال إلى موضع معين
  Future<void> seek(Duration position) async {
    await _audioService.seek(position);
  }

  /// تغيير مستوى الصوت
  Future<void> setVolume(double volume) async {
    await _audioService.setVolume(volume);
    _audioState = _audioState.copyWith(volume: volume);
    notifyListeners();
  }

  /// تغيير سرعة التشغيل
  Future<void> setSpeed(double speed) async {
    await _audioService.setSpeed(speed);
    _audioState = _audioState.copyWith(speed: speed);
    notifyListeners();
  }

  /// تحميل الصوت
  Future<void> downloadAudio(
    int surahNumber,
    String reciter,
    Function(double)? onProgress,
  ) async {
    await _audioService.downloadAudio(surahNumber, reciter, onProgress);
    
    // تحديث معلومات الصوت الحالي إذا كان نفس السورة
    if (_currentAudioInfo?.surahNumber == surahNumber) {
      _currentAudioInfo = _currentAudioInfo!.copyWith(
        isDownloaded: true,
      );
      notifyListeners();
    }
  }

  /// التحقق من تحميل الصوت
  Future<bool> isAudioDownloaded(int surahNumber, String reciter) async {
    return await _audioService.isAudioDownloaded(surahNumber, reciter);
  }

  /// حذف الصوت المحمل
  Future<void> deleteDownloadedAudio(int surahNumber, String reciter) async {
    await _audioService.deleteDownloadedAudio(surahNumber, reciter);
    
    // تحديث معلومات الصوت الحالي إذا كان نفس السورة
    if (_currentAudioInfo?.surahNumber == surahNumber) {
      _currentAudioInfo = _currentAudioInfo!.copyWith(
        isDownloaded: false,
      );
      notifyListeners();
    }
  }

  /// تبديل التشغيل/الإيقاف
  Future<void> togglePlayPause() async {
    if (_audioState.isPlaying) {
      await pause();
    } else if (_audioState.canPlay) {
      await resume();
    }
  }

  /// تحديد الآية الحالية (للتمييز أثناء التشغيل)
  void setCurrentAyah(int? ayahNumber) {
    _audioState = _audioState.copyWith(currentAyah: ayahNumber);
    notifyListeners();
  }

  /// الحصول على النسبة المئوية للتقدم
  double get progressPercentage {
    if (_audioState.duration.inMilliseconds == 0) return 0.0;
    return _audioState.position.inMilliseconds / _audioState.duration.inMilliseconds;
  }

  /// تنسيق الوقت
  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
