import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:quran_app/main.dart';

void main() {
  testWidgets('Quran app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const QuranApp());

    // Wait for the app to load
    await tester.pumpAndSettle();

    // Verify that the app title is displayed.
    expect(find.text('القرآن الكريم'), findsOneWidget);

    // Verify that the bottom navigation is present.
    expect(find.text('الرئيسية'), findsOneWidget);
    expect(find.text('السور'), findsOneWidget);
    expect(find.text('الأجزاء'), findsOneWidget);
    expect(find.text('المفضلة'), findsOneWidget);
    expect(find.text('الإعدادات'), findsOneWidget);
  });
}
