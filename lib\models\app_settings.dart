/// إعدادات التطبيق
class AppSettings {
  final bool isDarkMode;
  final double fontSize;
  final String fontFamily;
  final String language;
  final String reciter;
  final double audioSpeed;
  final bool autoScroll;
  final bool showTranslation;
  final bool enableNotifications;

  const AppSettings({
    this.isDarkMode = false,
    this.fontSize = 18.0,
    this.fontFamily = 'Amiri',
    this.language = 'ar',
    this.reciter = 'mishary',
    this.audioSpeed = 1.0,
    this.autoScroll = true,
    this.showTranslation = false,
    this.enableNotifications = true,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      isDarkMode: json['is_dark_mode'] as bool? ?? false,
      fontSize: (json['font_size'] as num?)?.toDouble() ?? 18.0,
      fontFamily: json['font_family'] as String? ?? 'Amiri',
      language: json['language'] as String? ?? 'ar',
      reciter: json['reciter'] as String? ?? 'mishary',
      audioSpeed: (json['audio_speed'] as num?)?.toDouble() ?? 1.0,
      autoScroll: json['auto_scroll'] as bool? ?? true,
      showTranslation: json['show_translation'] as bool? ?? false,
      enableNotifications: json['enable_notifications'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_dark_mode': isDarkMode,
      'font_size': fontSize,
      'font_family': fontFamily,
      'language': language,
      'reciter': reciter,
      'audio_speed': audioSpeed,
      'auto_scroll': autoScroll,
      'show_translation': showTranslation,
      'enable_notifications': enableNotifications,
    };
  }

  AppSettings copyWith({
    bool? isDarkMode,
    double? fontSize,
    String? fontFamily,
    String? language,
    String? reciter,
    double? audioSpeed,
    bool? autoScroll,
    bool? showTranslation,
    bool? enableNotifications,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      language: language ?? this.language,
      reciter: reciter ?? this.reciter,
      audioSpeed: audioSpeed ?? this.audioSpeed,
      autoScroll: autoScroll ?? this.autoScroll,
      showTranslation: showTranslation ?? this.showTranslation,
      enableNotifications: enableNotifications ?? this.enableNotifications,
    );
  }

  /// أحجام الخط المتاحة
  static const List<double> availableFontSizes = [
    14.0, 16.0, 18.0, 20.0, 22.0, 24.0, 26.0, 28.0, 30.0
  ];

  /// القراء المتاحون
  static const Map<String, String> availableReciters = {
    'mishary': 'مشاري العفاسي',
    'sudais': 'عبد الرحمن السديس',
    'shuraim': 'سعود الشريم',
    'maher': 'ماهر المعيقلي',
    'ajmi': 'أحمد العجمي',
  };
}
