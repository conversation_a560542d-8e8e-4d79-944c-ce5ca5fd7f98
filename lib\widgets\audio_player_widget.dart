import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';

/// ويدجت مشغل الصوت
class AudioPlayerWidget extends StatelessWidget {
  const AudioPlayerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        final audioInfo = audioProvider.currentAudioInfo;
        final audioState = audioProvider.audioState;

        if (audioInfo == null) return const SizedBox.shrink();

        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات السورة
                Row(
                  children: [
                    Icon(
                      Icons.play_circle_filled,
                      color: Theme.of(context).primaryColor,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            audioInfo.surahName,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            audioInfo.reciterName,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (audioInfo.isDownloaded)
                      Icon(
                        Icons.download_done,
                        color: Colors.green.shade600,
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // شريط التقدم
                Column(
                  children: [
                    SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                        trackHeight: 4,
                      ),
                      child: Slider(
                        value: audioProvider.progressPercentage.clamp(0.0, 1.0),
                        onChanged: audioState.duration.inMilliseconds > 0
                            ? (value) {
                                final position = Duration(
                                  milliseconds: (value * audioState.duration.inMilliseconds).round(),
                                );
                                audioProvider.seek(position);
                              }
                            : null,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            audioProvider.formatDuration(audioState.position),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            audioProvider.formatDuration(audioState.duration),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // أزرار التحكم
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر السرعة
                    IconButton(
                      onPressed: () => _showSpeedDialog(context, audioProvider),
                      icon: const Icon(Icons.speed),
                      tooltip: 'سرعة التشغيل',
                    ),

                    // زر الترجيع
                    IconButton(
                      onPressed: audioState.position.inSeconds > 10
                          ? () {
                              final newPosition = audioState.position - const Duration(seconds: 10);
                              audioProvider.seek(newPosition);
                            }
                          : null,
                      icon: const Icon(Icons.replay_10),
                      tooltip: 'ترجيع 10 ثوان',
                    ),

                    // زر التشغيل/الإيقاف
                    if (audioState.isLoading)
                      const CircularProgressIndicator()
                    else
                      IconButton(
                        onPressed: audioProvider.togglePlayPause,
                        icon: Icon(
                          audioState.isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                          size: 48,
                        ),
                        tooltip: audioState.isPlaying ? 'إيقاف مؤقت' : 'تشغيل',
                      ),

                    // زر التقديم
                    IconButton(
                      onPressed: audioState.position.inMilliseconds < 
                          audioState.duration.inMilliseconds - 10000
                          ? () {
                              final newPosition = audioState.position + const Duration(seconds: 10);
                              audioProvider.seek(newPosition);
                            }
                          : null,
                      icon: const Icon(Icons.forward_10),
                      tooltip: 'تقديم 10 ثوان',
                    ),

                    // زر الإيقاف
                    IconButton(
                      onPressed: audioProvider.stop,
                      icon: const Icon(Icons.stop),
                      tooltip: 'إيقاف',
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSpeedDialog(BuildContext context, AudioProvider audioProvider) {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سرعة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: speeds.map((speed) {
            return RadioListTile<double>(
              title: Text('${speed}x'),
              value: speed,
              groupValue: audioProvider.audioState.speed,
              onChanged: (value) {
                if (value != null) {
                  audioProvider.setSpeed(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }
}
