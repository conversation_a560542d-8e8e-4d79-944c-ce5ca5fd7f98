import 'package:just_audio/just_audio.dart';

/// حالة تشغيل الصوت
class AudioState {
  final bool isPlaying;
  final bool isLoading;
  final Duration position;
  final Duration duration;
  final int? currentSurah;
  final int? currentAyah;
  final double volume;
  final double speed;
  final ProcessingState processingState;

  const AudioState({
    this.isPlaying = false,
    this.isLoading = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.currentSurah,
    this.currentAyah,
    this.volume = 1.0,
    this.speed = 1.0,
    this.processingState = ProcessingState.idle,
  });

  AudioState copyWith({
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
    int? currentSurah,
    int? currentAyah,
    double? volume,
    double? speed,
    ProcessingState? processingState,
  }) {
    return AudioState(
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      currentSurah: currentSurah ?? this.currentSurah,
      currentAyah: currentAyah ?? this.currentAyah,
      volume: volume ?? this.volume,
      speed: speed ?? this.speed,
      processingState: processingState ?? this.processingState,
    );
  }

  /// هل الصوت متوقف تماماً
  bool get isStopped => processingState == ProcessingState.idle;

  /// هل الصوت مكتمل
  bool get isCompleted => processingState == ProcessingState.completed;

  /// هل يمكن التشغيل
  bool get canPlay => !isLoading && !isPlaying;

  /// هل يمكن الإيقاف المؤقت
  bool get canPause => isPlaying && !isLoading;
}

/// معلومات الصوت المحمل
class AudioInfo {
  final int surahNumber;
  final String surahName;
  final String reciterName;
  final String audioUrl;
  final bool isDownloaded;
  final String? localPath;

  const AudioInfo({
    required this.surahNumber,
    required this.surahName,
    required this.reciterName,
    required this.audioUrl,
    this.isDownloaded = false,
    this.localPath,
  });

  AudioInfo copyWith({
    int? surahNumber,
    String? surahName,
    String? reciterName,
    String? audioUrl,
    bool? isDownloaded,
    String? localPath,
  }) {
    return AudioInfo(
      surahNumber: surahNumber ?? this.surahNumber,
      surahName: surahName ?? this.surahName,
      reciterName: reciterName ?? this.reciterName,
      audioUrl: audioUrl ?? this.audioUrl,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localPath: localPath ?? this.localPath,
    );
  }
}
