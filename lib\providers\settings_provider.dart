import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings.dart';

/// مزود إعدادات التطبيق
class SettingsProvider with ChangeNotifier {
  static const String _settingsKey = 'app_settings';
  
  AppSettings _settings = const AppSettings();
  bool _isLoading = true;

  AppSettings get settings => _settings;
  bool get isLoading => _isLoading;
  bool get isDarkMode => _settings.isDarkMode;
  double get fontSize => _settings.fontSize;
  String get fontFamily => _settings.fontFamily;
  String get reciter => _settings.reciter;

  /// تحميل الإعدادات
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = AppSettings.fromJson(settingsMap);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(_settings.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('خطأ في حفظ الإعدادات: $e');
    }
  }

  /// تبديل الوضع الليلي/النهاري
  Future<void> toggleTheme() async {
    _settings = _settings.copyWith(isDarkMode: !_settings.isDarkMode);
    notifyListeners();
    await _saveSettings();
  }

  /// تغيير حجم الخط
  Future<void> setFontSize(double fontSize) async {
    if (AppSettings.availableFontSizes.contains(fontSize)) {
      _settings = _settings.copyWith(fontSize: fontSize);
      notifyListeners();
      await _saveSettings();
    }
  }

  /// تغيير نوع الخط
  Future<void> setFontFamily(String fontFamily) async {
    _settings = _settings.copyWith(fontFamily: fontFamily);
    notifyListeners();
    await _saveSettings();
  }

  /// تغيير القارئ
  Future<void> setReciter(String reciter) async {
    if (AppSettings.availableReciters.containsKey(reciter)) {
      _settings = _settings.copyWith(reciter: reciter);
      notifyListeners();
      await _saveSettings();
    }
  }

  /// تغيير سرعة الصوت
  Future<void> setAudioSpeed(double speed) async {
    _settings = _settings.copyWith(audioSpeed: speed);
    notifyListeners();
    await _saveSettings();
  }

  /// تبديل التمرير التلقائي
  Future<void> toggleAutoScroll() async {
    _settings = _settings.copyWith(autoScroll: !_settings.autoScroll);
    notifyListeners();
    await _saveSettings();
  }

  /// تبديل عرض الترجمة
  Future<void> toggleTranslation() async {
    _settings = _settings.copyWith(showTranslation: !_settings.showTranslation);
    notifyListeners();
    await _saveSettings();
  }

  /// تبديل الإشعارات
  Future<void> toggleNotifications() async {
    _settings = _settings.copyWith(enableNotifications: !_settings.enableNotifications);
    notifyListeners();
    await _saveSettings();
  }

  /// إعادة تعيين الإعدادات للافتراضية
  Future<void> resetSettings() async {
    _settings = const AppSettings();
    notifyListeners();
    await _saveSettings();
  }

  /// الحصول على سمة التطبيق
  ThemeData getTheme() {
    return _settings.isDarkMode ? _getDarkTheme() : _getLightTheme();
  }

  /// السمة الفاتحة
  ThemeData _getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: Colors.green,
      primaryColor: Colors.green.shade700,
      scaffoldBackgroundColor: Colors.grey.shade50,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontFamily: 'Roboto', // استخدام خط افتراضي مؤقتاً
          fontSize: _settings.fontSize,
          color: Colors.black87,
        ),
        bodyMedium: TextStyle(
          fontFamily: 'Roboto', // استخدام خط افتراضي مؤقتاً
          fontSize: _settings.fontSize - 2,
          color: Colors.black87,
        ),
        titleLarge: TextStyle(
          fontFamily: 'Roboto',
          fontSize: _settings.fontSize + 4,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        titleMedium: TextStyle(
          fontFamily: 'Roboto',
          fontSize: _settings.fontSize + 2,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// السمة الداكنة
  ThemeData _getDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: Colors.green,
      primaryColor: Colors.green.shade400,
      scaffoldBackgroundColor: Colors.grey.shade900,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.grey.shade800,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontFamily: 'Roboto', // استخدام خط افتراضي مؤقتاً
          fontSize: _settings.fontSize,
          color: Colors.white,
        ),
        bodyMedium: TextStyle(
          fontFamily: 'Roboto', // استخدام خط افتراضي مؤقتاً
          fontSize: _settings.fontSize - 2,
          color: Colors.white70,
        ),
        titleLarge: TextStyle(
          fontFamily: 'Roboto',
          fontSize: _settings.fontSize + 4,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        titleMedium: TextStyle(
          fontFamily: 'Roboto',
          fontSize: _settings.fontSize + 2,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 4,
        color: Colors.grey.shade800,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
