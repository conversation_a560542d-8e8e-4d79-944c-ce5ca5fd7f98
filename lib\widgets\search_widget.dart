import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';

/// ويدجت البحث السريع
class SearchWidget extends StatefulWidget {
  const SearchWidget({super.key});

  @override
  State<SearchWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // شريط البحث
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث في القرآن الكريم...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                context.read<QuranProvider>().clearSearch();
                                setState(() {
                                  _isExpanded = false;
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Theme.of(context).scaffoldBackgroundColor,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _isExpanded = value.isNotEmpty;
                      });
                      if (value.isNotEmpty) {
                        context.read<QuranProvider>().search(value);
                      } else {
                        context.read<QuranProvider>().clearSearch();
                      }
                    },
                    onSubmitted: (value) {
                      if (value.isNotEmpty) {
                        _performSearch(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    if (_searchController.text.isNotEmpty) {
                      _performSearch(_searchController.text);
                    }
                  },
                  icon: const Icon(Icons.search),
                  style: IconButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            // نتائج البحث السريع
            if (_isExpanded) ...[
              const SizedBox(height: 16),
              Consumer<QuranProvider>(
                builder: (context, quranProvider, child) {
                  if (quranProvider.isSearching) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  final results = quranProvider.searchResults.take(5).toList();
                  
                  if (results.isEmpty && _searchController.text.isNotEmpty) {
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        'لا توجد نتائج',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نتائج البحث السريع',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...results.map((result) => _buildSearchResultItem(result)),
                      if (quranProvider.searchResults.length > 5)
                        TextButton(
                          onPressed: () {
                            // فتح صفحة النتائج الكاملة
                            _showFullSearchResults();
                          },
                          child: Text('عرض جميع النتائج (${quranProvider.searchResults.length})'),
                        ),
                    ],
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultItem(result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 4),
      child: ListTile(
        dense: true,
        title: Text(
          result.surah.nameArabic,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'الآية ${result.ayah.number}',
          style: const TextStyle(fontSize: 12),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _navigateToResult(result),
      ),
    );
  }

  void _performSearch(String query) {
    // فتح صفحة البحث الكاملة
    showSearch(
      context: context,
      delegate: QuranSearchDelegate(initialQuery: query),
    );
  }

  void _showFullSearchResults() {
    // عرض جميع نتائج البحث
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchResultsScreen(
          query: _searchController.text,
        ),
      ),
    );
  }

  void _navigateToResult(result) {
    // الانتقال إلى نتيجة البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الانتقال إلى ${result.surah.nameArabic} - الآية ${result.ayah.number}'),
      ),
    );
  }
}

/// مفوض البحث المحسن
class QuranSearchDelegate extends SearchDelegate<String> {
  final String? initialQuery;

  QuranSearchDelegate({this.initialQuery}) {
    if (initialQuery != null) {
      query = initialQuery!;
    }
  }

  @override
  String get searchFieldLabel => 'البحث في القرآن الكريم...';

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return SearchResultsScreen(query: query);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return buildResults(context);
  }
}

/// شاشة نتائج البحث
class SearchResultsScreen extends StatelessWidget {
  final String query;

  const SearchResultsScreen({super.key, required this.query});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('نتائج البحث: "$query"'),
      ),
      body: Consumer<QuranProvider>(
        builder: (context, quranProvider, child) {
          // تنفيذ البحث
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (query.isNotEmpty) {
              quranProvider.search(query);
            }
          });

          if (quranProvider.isSearching) {
            return const Center(child: CircularProgressIndicator());
          }

          final results = quranProvider.searchResults;

          if (results.isEmpty) {
            return const Center(
              child: Text('لا توجد نتائج للبحث'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final result = results[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text(result.surah.nameArabic),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('الآية ${result.ayah.number}'),
                      const SizedBox(height: 4),
                      Text(
                        result.ayah.textUthmani,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.8,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  isThreeLine: true,
                  onTap: () {
                    // الانتقال إلى الآية
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('الانتقال إلى ${result.surah.nameArabic} - الآية ${result.ayah.number}'),
                      ),
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
