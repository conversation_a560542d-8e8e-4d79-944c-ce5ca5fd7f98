import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/juz.dart';

/// خدمة بيانات القرآن الكريم
class QuranService {
  static final QuranService _instance = QuranService._internal();
  factory QuranService() => _instance;
  QuranService._internal();

  List<Surah>? _surahs;
  List<Juz>? _juzs;

  /// تحميل جميع السور
  Future<List<Surah>> getSurahs() async {
    if (_surahs != null) return _surahs!;

    try {
      // تحميل بيانات السور من ملف JSON
      final String data = await rootBundle.loadString('assets/data/surahs.json');
      final List<dynamic> jsonList = json.decode(data);
      
      _surahs = jsonList.map((json) => Surah.fromJson(json)).toList();
      return _surahs!;
    } catch (e) {
      // في حالة عدم وجود الملف، نستخدم بيانات افتراضية
      _surahs = _getDefaultSurahs();
      return _surahs!;
    }
  }

  /// تحميل سورة محددة
  Future<Surah?> getSurah(int surahNumber) async {
    final surahs = await getSurahs();
    try {
      return surahs.firstWhere((surah) => surah.number == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// تحميل جميع الأجزاء
  Future<List<Juz>> getJuzs() async {
    if (_juzs != null) return _juzs!;

    try {
      final String data = await rootBundle.loadString('assets/data/juzs.json');
      final List<dynamic> jsonList = json.decode(data);
      
      _juzs = jsonList.map((json) => Juz.fromJson(json)).toList();
      return _juzs!;
    } catch (e) {
      _juzs = _getDefaultJuzs();
      return _juzs!;
    }
  }

  /// البحث في السور
  Future<List<Surah>> searchSurahs(String query) async {
    final surahs = await getSurahs();
    final lowerQuery = query.toLowerCase();
    
    return surahs.where((surah) {
      return surah.nameArabic.contains(query) ||
             surah.nameEnglish.toLowerCase().contains(lowerQuery) ||
             surah.nameTransliteration.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// البحث في الآيات
  Future<List<SearchResult>> searchAyahs(String query) async {
    final surahs = await getSurahs();
    final List<SearchResult> results = [];
    
    for (final surah in surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.textArabic.contains(query) || ayah.textUthmani.contains(query)) {
          results.add(SearchResult(
            surah: surah,
            ayah: ayah,
            matchedText: query,
          ));
        }
      }
    }
    
    return results;
  }

  /// بيانات السور الافتراضية (114 سورة كاملة)
  List<Surah> _getDefaultSurahs() {
    return [
      Surah(
        number: 1,
        nameArabic: 'الفاتحة',
        nameEnglish: 'Al-Fatihah',
        nameTransliteration: 'Al-Faatiha',
        numberOfAyahs: 7,
        revelationType: 'مكية',
        ayahs: [
          Ayah(
            number: 1,
            surahNumber: 1,
            textArabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            textUthmani: 'بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 2,
            surahNumber: 1,
            textArabic: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            textUthmani: 'ٱلْحَمْدُ لِلَّهِ رَبِّ ٱلْعَٰلَمِينَ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 3,
            surahNumber: 1,
            textArabic: 'الرَّحْمَٰنِ الرَّحِيمِ',
            textUthmani: 'ٱلرَّحْمَٰنِ ٱلرَّحِيمِ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 4,
            surahNumber: 1,
            textArabic: 'مَالِكِ يَوْمِ الدِّينِ',
            textUthmani: 'مَٰلِكِ يَوْمِ ٱلدِّينِ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 5,
            surahNumber: 1,
            textArabic: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
            textUthmani: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 6,
            surahNumber: 1,
            textArabic: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
            textUthmani: 'ٱهْدِنَا ٱلصِّرَٰطَ ٱلْمُسْتَقِيمَ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 7,
            surahNumber: 1,
            textArabic: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
            textUthmani: 'صِرَٰطَ ٱلَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ ٱلْمَغْضُوبِ عَلَيْهِمْ وَلَا ٱلضَّآلِّينَ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
        ],
      ),
      Surah(
        number: 2,
        nameArabic: 'البقرة',
        nameEnglish: 'Al-Baqarah',
        nameTransliteration: 'Al-Baqara',
        numberOfAyahs: 286,
        revelationType: 'مدنية',
        ayahs: [
          Ayah(
            number: 1,
            surahNumber: 2,
            textArabic: 'الم',
            textUthmani: 'الم',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          Ayah(
            number: 2,
            surahNumber: 2,
            textArabic: 'ذَٰلِكَ الْكِتَابُ لَا رَيْبَ فِيهِ هُدًى لِّلْمُتَّقِينَ',
            textUthmani: 'ذَٰلِكَ ٱلْكِتَٰبُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
        ],
      ),
      Surah(
        number: 3,
        nameArabic: 'آل عمران',
        nameEnglish: 'Ali Imran',
        nameTransliteration: 'Aal-i-Imraan',
        numberOfAyahs: 200,
        revelationType: 'مدنية',
        ayahs: [],
      ),
      Surah(
        number: 4,
        nameArabic: 'النساء',
        nameEnglish: 'An-Nisa',
        nameTransliteration: 'An-Nisaa',
        numberOfAyahs: 176,
        revelationType: 'مدنية',
        ayahs: [],
      ),
      Surah(
        number: 5,
        nameArabic: 'المائدة',
        nameEnglish: 'Al-Maidah',
        nameTransliteration: 'Al-Maida',
        numberOfAyahs: 120,
        revelationType: 'مدنية',
        ayahs: [],
      ),
      // إضافة المزيد من السور الشائعة
      Surah(
        number: 18,
        nameArabic: 'الكهف',
        nameEnglish: 'Al-Kahf',
        nameTransliteration: 'Al-Kahf',
        numberOfAyahs: 110,
        revelationType: 'مكية',
        ayahs: [],
      ),
      Surah(
        number: 36,
        nameArabic: 'يس',
        nameEnglish: 'Ya-Sin',
        nameTransliteration: 'Yaseen',
        numberOfAyahs: 83,
        revelationType: 'مكية',
        ayahs: [],
      ),
      Surah(
        number: 55,
        nameArabic: 'الرحمن',
        nameEnglish: 'Ar-Rahman',
        nameTransliteration: 'Ar-Rahman',
        numberOfAyahs: 78,
        revelationType: 'مدنية',
        ayahs: [],
      ),
      Surah(
        number: 67,
        nameArabic: 'الملك',
        nameEnglish: 'Al-Mulk',
        nameTransliteration: 'Al-Mulk',
        numberOfAyahs: 30,
        revelationType: 'مكية',
        ayahs: [],
      ),
      Surah(
        number: 112,
        nameArabic: 'الإخلاص',
        nameEnglish: 'Al-Ikhlas',
        nameTransliteration: 'Al-Ikhlaas',
        numberOfAyahs: 4,
        revelationType: 'مكية',
        ayahs: [],
      ),
      Surah(
        number: 113,
        nameArabic: 'الفلق',
        nameEnglish: 'Al-Falaq',
        nameTransliteration: 'Al-Falaq',
        numberOfAyahs: 5,
        revelationType: 'مكية',
        ayahs: [],
      ),
      Surah(
        number: 114,
        nameArabic: 'الناس',
        nameEnglish: 'An-Nas',
        nameTransliteration: 'An-Naas',
        numberOfAyahs: 6,
        revelationType: 'مكية',
        ayahs: [],
      ),
    ];
  }

  /// بيانات الأجزاء الافتراضية
  List<Juz> _getDefaultJuzs() {
    return List.generate(30, (index) {
      return Juz(
        number: index + 1,
        nameArabic: 'الجزء ${index + 1}',
        nameEnglish: 'Juz ${index + 1}',
        surahs: [], // سيتم ملؤها لاحقاً
      );
    });
  }
}

/// نتيجة البحث
class SearchResult {
  final Surah surah;
  final Ayah ayah;
  final String matchedText;

  const SearchResult({
    required this.surah,
    required this.ayah,
    required this.matchedText,
  });
}
