import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/juz.dart';

/// خدمة بيانات القرآن الكريم
class QuranService {
  static final QuranService _instance = QuranService._internal();
  factory QuranService() => _instance;
  QuranService._internal();

  List<Surah>? _surahs;
  List<Juz>? _juzs;

  /// تحميل جميع السور
  Future<List<Surah>> getSurahs() async {
    if (_surahs != null) return _surahs!;

    try {
      // تحميل بيانات السور من ملف JSON
      final String data = await rootBundle.loadString('assets/data/surahs.json');
      final List<dynamic> jsonList = json.decode(data);
      
      _surahs = jsonList.map((json) => Surah.fromJson(json)).toList();
      return _surahs!;
    } catch (e) {
      // في حالة عدم وجود الملف، نستخدم بيانات افتراضية
      _surahs = _getDefaultSurahs();
      return _surahs!;
    }
  }

  /// تحميل سورة محددة
  Future<Surah?> getSurah(int surahNumber) async {
    final surahs = await getSurahs();
    try {
      return surahs.firstWhere((surah) => surah.number == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// تحميل جميع الأجزاء
  Future<List<Juz>> getJuzs() async {
    if (_juzs != null) return _juzs!;

    try {
      final String data = await rootBundle.loadString('assets/data/juzs.json');
      final List<dynamic> jsonList = json.decode(data);
      
      _juzs = jsonList.map((json) => Juz.fromJson(json)).toList();
      return _juzs!;
    } catch (e) {
      _juzs = _getDefaultJuzs();
      return _juzs!;
    }
  }

  /// البحث في السور
  Future<List<Surah>> searchSurahs(String query) async {
    final surahs = await getSurahs();
    final lowerQuery = query.toLowerCase();
    
    return surahs.where((surah) {
      return surah.nameArabic.contains(query) ||
             surah.nameEnglish.toLowerCase().contains(lowerQuery) ||
             surah.nameTransliteration.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// البحث في الآيات
  Future<List<SearchResult>> searchAyahs(String query) async {
    final surahs = await getSurahs();
    final List<SearchResult> results = [];
    
    for (final surah in surahs) {
      for (final ayah in surah.ayahs) {
        if (ayah.textArabic.contains(query) || ayah.textUthmani.contains(query)) {
          results.add(SearchResult(
            surah: surah,
            ayah: ayah,
            matchedText: query,
          ));
        }
      }
    }
    
    return results;
  }

  /// بيانات السور الافتراضية (أول 10 سور كمثال)
  List<Surah> _getDefaultSurahs() {
    return [
      Surah(
        number: 1,
        nameArabic: 'الفاتحة',
        nameEnglish: 'Al-Fatihah',
        nameTransliteration: 'Al-Faatiha',
        numberOfAyahs: 7,
        revelationType: 'مكية',
        ayahs: [
          Ayah(
            number: 1,
            surahNumber: 1,
            textArabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            textUthmani: 'بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ',
            juzNumber: 1,
            hizbNumber: 1,
            rubNumber: 1,
            sajdah: 0,
          ),
          // باقي آيات الفاتحة...
        ],
      ),
      // باقي السور...
    ];
  }

  /// بيانات الأجزاء الافتراضية
  List<Juz> _getDefaultJuzs() {
    return List.generate(30, (index) {
      return Juz(
        number: index + 1,
        nameArabic: 'الجزء ${index + 1}',
        nameEnglish: 'Juz ${index + 1}',
        surahs: [], // سيتم ملؤها لاحقاً
      );
    });
  }
}

/// نتيجة البحث
class SearchResult {
  final Surah surah;
  final Ayah ayah;
  final String matchedText;

  const SearchResult({
    required this.surah,
    required this.ayah,
    required this.matchedText,
  });
}
