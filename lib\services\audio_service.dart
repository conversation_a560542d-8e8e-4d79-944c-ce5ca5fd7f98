import 'dart:io';
import 'package:just_audio/just_audio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';

/// خدمة تشغيل الصوت
class QuranAudioService {
  static final QuranAudioService _instance = QuranAudioService._internal();
  factory QuranAudioService() => _instance;
  QuranAudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final Dio _dio = Dio();

  /// الحصول على مشغل الصوت
  AudioPlayer get audioPlayer => _audioPlayer;

  /// تشغيل سورة
  Future<void> playSurah(int surahNumber, String reciter) async {
    try {
      final audioUrl = _getAudioUrl(surahNumber, reciter);
      
      // التحقق من وجود الملف محلياً أولاً
      final localPath = await _getLocalAudioPath(surahNumber, reciter);
      final localFile = File(localPath);
      
      String sourceUrl;
      if (await localFile.exists()) {
        sourceUrl = localPath;
      } else {
        sourceUrl = audioUrl;
      }
      
      await _audioPlayer.setUrl(sourceUrl);
      await _audioPlayer.play();
    } catch (e) {
      throw Exception('خطأ في تشغيل الصوت: $e');
    }
  }

  /// إيقاف التشغيل
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// استئناف التشغيل
  Future<void> resume() async {
    await _audioPlayer.play();
  }

  /// إيقاف التشغيل نهائياً
  Future<void> stop() async {
    await _audioPlayer.stop();
  }

  /// الانتقال إلى موضع معين
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// تغيير مستوى الصوت
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume);
  }

  /// تغيير سرعة التشغيل
  Future<void> setSpeed(double speed) async {
    await _audioPlayer.setSpeed(speed);
  }

  /// تحميل الصوت للاستخدام دون اتصال
  Future<void> downloadAudio(
    int surahNumber, 
    String reciter,
    Function(double)? onProgress,
  ) async {
    try {
      final audioUrl = _getAudioUrl(surahNumber, reciter);
      final localPath = await _getLocalAudioPath(surahNumber, reciter);
      final localFile = File(localPath);
      
      // إنشاء المجلد إذا لم يكن موجوداً
      await localFile.parent.create(recursive: true);
      
      await _dio.download(
        audioUrl,
        localPath,
        onReceiveProgress: (received, total) {
          if (total != -1 && onProgress != null) {
            onProgress(received / total);
          }
        },
      );
    } catch (e) {
      throw Exception('خطأ في تحميل الصوت: $e');
    }
  }

  /// التحقق من تحميل الصوت
  Future<bool> isAudioDownloaded(int surahNumber, String reciter) async {
    final localPath = await _getLocalAudioPath(surahNumber, reciter);
    return File(localPath).exists();
  }

  /// حذف الصوت المحمل
  Future<void> deleteDownloadedAudio(int surahNumber, String reciter) async {
    final localPath = await _getLocalAudioPath(surahNumber, reciter);
    final localFile = File(localPath);
    if (await localFile.exists()) {
      await localFile.delete();
    }
  }

  /// الحصول على رابط الصوت
  String _getAudioUrl(int surahNumber, String reciter) {
    // روابط الصوت من موقع القرآن الكريم
    final Map<String, String> reciterUrls = {
      'mishary': 'https://server8.mp3quran.net/afs',
      'sudais': 'https://server11.mp3quran.net/sds',
      'shuraim': 'https://server12.mp3quran.net/shr',
      'maher': 'https://server12.mp3quran.net/maher',
      'ajmi': 'https://server10.mp3quran.net/ajm',
    };
    
    final baseUrl = reciterUrls[reciter] ?? reciterUrls['mishary']!;
    final surahNumberPadded = surahNumber.toString().padLeft(3, '0');
    
    return '$baseUrl/$surahNumberPadded.mp3';
  }

  /// الحصول على مسار الملف المحلي
  Future<String> _getLocalAudioPath(int surahNumber, String reciter) async {
    final directory = await getApplicationDocumentsDirectory();
    final surahNumberPadded = surahNumber.toString().padLeft(3, '0');
    return '${directory.path}/audio/$reciter/$surahNumberPadded.mp3';
  }

  /// تنظيف الموارد
  void dispose() {
    _audioPlayer.dispose();
  }
}
