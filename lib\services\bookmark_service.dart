import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/bookmark.dart';

/// خدمة الإشارات المرجعية
class BookmarkService {
  static final BookmarkService _instance = BookmarkService._internal();
  factory BookmarkService() => _instance;
  BookmarkService._internal();

  static const String _bookmarksKey = 'bookmarks';
  List<Bookmark>? _cachedBookmarks;

  /// الحصول على جميع الإشارات المرجعية
  Future<List<Bookmark>> getBookmarks() async {
    if (_cachedBookmarks != null) return _cachedBookmarks!;

    final prefs = await SharedPreferences.getInstance();
    final bookmarksJson = prefs.getStringList(_bookmarksKey) ?? [];
    
    _cachedBookmarks = bookmarksJson
        .map((json) => Bookmark.fromJson(jsonDecode(json)))
        .toList();
    
    // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
    _cachedBookmarks!.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return _cachedBookmarks!;
  }

  /// إضافة إشارة مرجعية
  Future<void> addBookmark(Bookmark bookmark) async {
    final bookmarks = await getBookmarks();
    
    // التحقق من عدم وجود الإشارة مسبقاً
    if (!bookmarks.any((b) => b.id == bookmark.id)) {
      bookmarks.insert(0, bookmark); // إضافة في المقدمة
      await _saveBookmarks(bookmarks);
      _cachedBookmarks = bookmarks;
    }
  }

  /// حذف إشارة مرجعية
  Future<void> removeBookmark(String bookmarkId) async {
    final bookmarks = await getBookmarks();
    bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
    await _saveBookmarks(bookmarks);
    _cachedBookmarks = bookmarks;
  }

  /// التحقق من وجود إشارة مرجعية
  Future<bool> isBookmarked(int surahNumber, int ayahNumber) async {
    final bookmarks = await getBookmarks();
    final bookmarkId = '${surahNumber}_$ayahNumber';
    return bookmarks.any((bookmark) => bookmark.id == bookmarkId);
  }

  /// تبديل حالة الإشارة المرجعية
  Future<bool> toggleBookmark({
    required int surahNumber,
    required int ayahNumber,
    required String surahName,
    required String ayahText,
    String? note,
  }) async {
    final bookmarkId = '${surahNumber}_$ayahNumber';
    final isCurrentlyBookmarked = await isBookmarked(surahNumber, ayahNumber);
    
    if (isCurrentlyBookmarked) {
      await removeBookmark(bookmarkId);
      return false;
    } else {
      final bookmark = Bookmark.fromAyah(
        surahNumber: surahNumber,
        ayahNumber: ayahNumber,
        surahName: surahName,
        ayahText: ayahText,
        note: note,
      );
      await addBookmark(bookmark);
      return true;
    }
  }

  /// البحث في الإشارات المرجعية
  Future<List<Bookmark>> searchBookmarks(String query) async {
    final bookmarks = await getBookmarks();
    final lowerQuery = query.toLowerCase();
    
    return bookmarks.where((bookmark) {
      return bookmark.surahName.contains(query) ||
             bookmark.ayahText.contains(query) ||
             (bookmark.note?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// الحصول على إشارات مرجعية لسورة معينة
  Future<List<Bookmark>> getBookmarksForSurah(int surahNumber) async {
    final bookmarks = await getBookmarks();
    return bookmarks
        .where((bookmark) => bookmark.surahNumber == surahNumber)
        .toList();
  }

  /// تحديث ملاحظة إشارة مرجعية
  Future<void> updateBookmarkNote(String bookmarkId, String? note) async {
    final bookmarks = await getBookmarks();
    final index = bookmarks.indexWhere((b) => b.id == bookmarkId);
    
    if (index != -1) {
      final updatedBookmark = Bookmark(
        id: bookmarks[index].id,
        surahNumber: bookmarks[index].surahNumber,
        ayahNumber: bookmarks[index].ayahNumber,
        surahName: bookmarks[index].surahName,
        ayahText: bookmarks[index].ayahText,
        createdAt: bookmarks[index].createdAt,
        note: note,
      );
      
      bookmarks[index] = updatedBookmark;
      await _saveBookmarks(bookmarks);
      _cachedBookmarks = bookmarks;
    }
  }

  /// حفظ الإشارات المرجعية
  Future<void> _saveBookmarks(List<Bookmark> bookmarks) async {
    final prefs = await SharedPreferences.getInstance();
    final bookmarksJson = bookmarks
        .map((bookmark) => jsonEncode(bookmark.toJson()))
        .toList();
    await prefs.setStringList(_bookmarksKey, bookmarksJson);
  }

  /// مسح جميع الإشارات المرجعية
  Future<void> clearAllBookmarks() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_bookmarksKey);
    _cachedBookmarks = [];
  }

  /// الحصول على عدد الإشارات المرجعية
  Future<int> getBookmarksCount() async {
    final bookmarks = await getBookmarks();
    return bookmarks.length;
  }
}
